{"level":30,"time":1759021459509,"pid":53672,"hostname":"<PERSON><PERSON><PERSON>","msg":"register transformer: Anthropic (endpoint: /v1/messages)"}
{"level":30,"time":1759021459509,"pid":53672,"hostname":"<PERSON>ele<PERSON>","msg":"register transformer: gemini (endpoint: /v1beta/models/:modelAndAction)"}
{"level":30,"time":1759021459509,"pid":53672,"hostname":"<PERSON>ele<PERSON>","msg":"register transformer: vertex-gemini (no endpoint)"}
{"level":30,"time":1759021459509,"pid":53672,"hostname":"Velen","msg":"register transformer: vertex-claude (no endpoint)"}
{"level":30,"time":1759021459509,"pid":53672,"hostname":"<PERSON><PERSON><PERSON>","msg":"register transformer: deepseek (no endpoint)"}
{"level":30,"time":1759021459509,"pid":53672,"hostname":"<PERSON>ele<PERSON>","msg":"register transformer: tooluse (no endpoint)"}
{"level":30,"time":1759021459509,"pid":53672,"hostname":"Velen","msg":"register transformer: openrouter (no endpoint)"}
{"level":30,"time":1759021459509,"pid":53672,"hostname":"Velen","msg":"register transformer: OpenAI (endpoint: /v1/chat/completions)"}
{"level":30,"time":1759021459509,"pid":53672,"hostname":"Velen","msg":"register transformer: maxtoken (no endpoint)"}
{"level":30,"time":1759021459509,"pid":53672,"hostname":"Velen","msg":"register transformer: groq (no endpoint)"}
{"level":30,"time":1759021459509,"pid":53672,"hostname":"Velen","msg":"register transformer: cleancache (no endpoint)"}
{"level":30,"time":1759021459509,"pid":53672,"hostname":"Velen","msg":"register transformer: enhancetool (no endpoint)"}
{"level":30,"time":1759021459509,"pid":53672,"hostname":"Velen","msg":"register transformer: reasoning (no endpoint)"}
{"level":30,"time":1759021459509,"pid":53672,"hostname":"Velen","msg":"register transformer: sampling (no endpoint)"}
{"level":30,"time":1759021459509,"pid":53672,"hostname":"Velen","msg":"register transformer: maxcompletiontokens (no endpoint)"}
{"level":30,"time":1759021459509,"pid":53672,"hostname":"Velen","msg":"register transformer: cerebras (no endpoint)"}
{"level":30,"time":1759021459509,"pid":53672,"hostname":"Velen","msg":"register transformer: streamoptions (no endpoint)"}
{"level":30,"time":1759021459509,"pid":53672,"hostname":"Velen","msg":"register transformer: customparams (no endpoint)"}
{"level":30,"time":1759021459516,"pid":53672,"hostname":"Velen","msg":"Iflow provider registered"}
{"level":30,"time":1759021459516,"pid":53672,"hostname":"Velen","msg":"魔搭全能 provider registered"}
{"level":30,"time":1759021459516,"pid":53672,"hostname":"Velen","msg":"魔搭快速 provider registered"}
{"level":30,"time":1759021459516,"pid":53672,"hostname":"Velen","msg":"魔搭思考 provider registered"}
{"level":30,"time":1759021459516,"pid":53672,"hostname":"Velen","msg":"魔搭共享 provider registered"}
{"level":30,"time":1759021459516,"pid":53672,"hostname":"Velen","msg":"gemini-pro provider registered"}
{"level":30,"time":1759021459516,"pid":53672,"hostname":"Velen","msg":"gemini-flash provider registered"}
{"level":30,"time":1759021459516,"pid":53672,"hostname":"Velen","msg":"GeminiTier1 provider registered"}
{"level":30,"time":1759021459582,"pid":53672,"hostname":"Velen","msg":"Server listening at http://127.0.0.1:2526"}
{"level":30,"time":1759021459582,"pid":53672,"hostname":"Velen","msg":"🚀 LLMs API server listening on http://127.0.0.1:2526"}
{"level":30,"time":1759021506654,"pid":53672,"hostname":"Velen","reqId":"req-1","req":{"method":"POST","url":"/api/config","host":"127.0.0.1:2526","remoteAddress":"127.0.0.1","remotePort":9167},"msg":"incoming request"}
{"level":30,"time":1759021506668,"pid":53672,"hostname":"Velen","reqId":"req-1","res":{"statusCode":200},"responseTime":13.599800050258636,"msg":"request completed"}
{"level":30,"time":1759021506673,"pid":53672,"hostname":"Velen","reqId":"req-2","req":{"method":"POST","url":"/api/restart","host":"127.0.0.1:2526","remoteAddress":"127.0.0.1","remotePort":9167},"msg":"incoming request"}
{"level":30,"time":1759021506674,"pid":53672,"hostname":"Velen","reqId":"req-2","res":{"statusCode":200},"responseTime":0.9456999897956848,"msg":"request completed"}
