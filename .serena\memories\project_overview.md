# Claude Code Router 项目概述

## 项目目的
这是一个基于Claude Code Router (CCR)的AI智能路由系统，名为Velen AI智能路由器。主要功能是根据用户请求的内容、复杂度和上下文长度，智能选择最合适的AI模型进行处理。

## 技术栈
- **核心框架**: Node.js + Claude Code Router
- **路由逻辑**: JavaScript (velen_router.js)
- **配置管理**: JSON配置文件
- **AI模型**: 多个供应商的AI模型（魔搭、Iflow、GeminiTier1等）

## 核心文件
- `velen_router.js`: 主要的智能路由逻辑
- `config.json`: 系统配置文件，包含供应商和模型配置
- `README_VELEN_AI_ROUTER.md`: 详细的系统文档

## 当前问题
用户报告预处理模型调用错误，预处理应该使用默认配置中的后台模型，但现在变成了Qwen/Qwen3-235B-A22B-Instruct-2507或gemini-2.5-pro。

## 系统架构
- AI预处理机制：由AI先处理决定如何选择模型
- 上下文与复杂度判断：分析请求复杂度和上下文需求
- 模型映射：根据分析结果映射到最合适的模型
- 指令执行：完成最终的模型调用