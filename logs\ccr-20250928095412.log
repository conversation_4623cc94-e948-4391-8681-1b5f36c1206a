{"level":30,"time":1759024452339,"pid":22624,"hostname":"<PERSON><PERSON><PERSON>","msg":"register transformer: An<PERSON><PERSON> (endpoint: /v1/messages)"}
{"level":30,"time":1759024452339,"pid":22624,"hostname":"<PERSON><PERSON><PERSON>","msg":"register transformer: gemini (endpoint: /v1beta/models/:modelAndAction)"}
{"level":30,"time":1759024452339,"pid":22624,"hostname":"<PERSON>ele<PERSON>","msg":"register transformer: vertex-gemini (no endpoint)"}
{"level":30,"time":1759024452339,"pid":22624,"hostname":"<PERSON>ele<PERSON>","msg":"register transformer: vertex-claude (no endpoint)"}
{"level":30,"time":1759024452339,"pid":22624,"hostname":"<PERSON>ele<PERSON>","msg":"register transformer: deepseek (no endpoint)"}
{"level":30,"time":1759024452339,"pid":22624,"hostname":"<PERSON>elen","msg":"register transformer: tooluse (no endpoint)"}
{"level":30,"time":1759024452339,"pid":22624,"hostname":"Velen","msg":"register transformer: openrouter (no endpoint)"}
{"level":30,"time":1759024452339,"pid":22624,"hostname":"Velen","msg":"register transformer: OpenAI (endpoint: /v1/chat/completions)"}
{"level":30,"time":1759024452339,"pid":22624,"hostname":"Velen","msg":"register transformer: maxtoken (no endpoint)"}
{"level":30,"time":1759024452339,"pid":22624,"hostname":"Velen","msg":"register transformer: groq (no endpoint)"}
{"level":30,"time":1759024452339,"pid":22624,"hostname":"Velen","msg":"register transformer: cleancache (no endpoint)"}
{"level":30,"time":1759024452339,"pid":22624,"hostname":"Velen","msg":"register transformer: enhancetool (no endpoint)"}
{"level":30,"time":1759024452340,"pid":22624,"hostname":"Velen","msg":"register transformer: reasoning (no endpoint)"}
{"level":30,"time":1759024452340,"pid":22624,"hostname":"Velen","msg":"register transformer: sampling (no endpoint)"}
{"level":30,"time":1759024452340,"pid":22624,"hostname":"Velen","msg":"register transformer: maxcompletiontokens (no endpoint)"}
{"level":30,"time":1759024452340,"pid":22624,"hostname":"Velen","msg":"register transformer: cerebras (no endpoint)"}
{"level":30,"time":1759024452340,"pid":22624,"hostname":"Velen","msg":"register transformer: streamoptions (no endpoint)"}
{"level":30,"time":1759024452340,"pid":22624,"hostname":"Velen","msg":"register transformer: customparams (no endpoint)"}
{"level":30,"time":1759024452346,"pid":22624,"hostname":"Velen","msg":"Iflow provider registered"}
{"level":30,"time":1759024452346,"pid":22624,"hostname":"Velen","msg":"魔搭全能 provider registered"}
{"level":30,"time":1759024452346,"pid":22624,"hostname":"Velen","msg":"魔搭快速 provider registered"}
{"level":30,"time":1759024452346,"pid":22624,"hostname":"Velen","msg":"魔搭思考 provider registered"}
{"level":30,"time":1759024452346,"pid":22624,"hostname":"Velen","msg":"魔搭共享 provider registered"}
{"level":30,"time":1759024452346,"pid":22624,"hostname":"Velen","msg":"gemini-pro provider registered"}
{"level":30,"time":1759024452346,"pid":22624,"hostname":"Velen","msg":"gemini-flash provider registered"}
{"level":30,"time":1759024452346,"pid":22624,"hostname":"Velen","msg":"GeminiTier1 provider registered"}
{"level":30,"time":1759024452413,"pid":22624,"hostname":"Velen","msg":"Server listening at http://127.0.0.1:2526"}
{"level":30,"time":1759024452414,"pid":22624,"hostname":"Velen","msg":"🚀 LLMs API server listening on http://127.0.0.1:2526"}
{"level":30,"time":1759026089205,"pid":22624,"hostname":"Velen","reqId":"req-1","req":{"method":"GET","url":"/ui/","host":"127.0.0.1:2526","remoteAddress":"127.0.0.1","remotePort":4524},"msg":"incoming request"}
{"level":30,"time":1759026089217,"pid":22624,"hostname":"Velen","reqId":"req-1","res":{"statusCode":304},"responseTime":11.369400024414062,"msg":"request completed"}
{"level":30,"time":1759026089373,"pid":22624,"hostname":"Velen","reqId":"req-2","req":{"method":"GET","url":"/api/config","host":"127.0.0.1:2526","remoteAddress":"127.0.0.1","remotePort":4524},"msg":"incoming request"}
{"level":30,"time":1759026089383,"pid":22624,"hostname":"Velen","reqId":"req-2","res":{"statusCode":200},"responseTime":9.594099998474121,"msg":"request completed"}
{"level":30,"time":1759026089385,"pid":22624,"hostname":"Velen","reqId":"req-3","req":{"method":"GET","url":"/favicon.ico","host":"127.0.0.1:2526","remoteAddress":"127.0.0.1","remotePort":4525},"msg":"incoming request"}
{"level":30,"time":1759026089386,"pid":22624,"hostname":"Velen","reqId":"req-3","res":{"statusCode":401},"responseTime":0.8875000476837158,"msg":"request completed"}
{"level":30,"time":1759026089388,"pid":22624,"hostname":"Velen","reqId":"req-4","req":{"method":"GET","url":"/api/config","host":"127.0.0.1:2526","remoteAddress":"127.0.0.1","remotePort":4526},"msg":"incoming request"}
{"level":30,"time":1759026089394,"pid":22624,"hostname":"Velen","reqId":"req-4","res":{"statusCode":200},"responseTime":5.883499979972839,"msg":"request completed"}
{"level":30,"time":1759026089399,"pid":22624,"hostname":"Velen","reqId":"req-5","req":{"method":"GET","url":"/api/update/check","host":"127.0.0.1:2526","remoteAddress":"127.0.0.1","remotePort":4524},"msg":"incoming request"}
{"level":30,"time":1759026089526,"pid":22624,"hostname":"Velen","reqId":"req-6","req":{"method":"GET","url":"/api/transformers","host":"127.0.0.1:2526","remoteAddress":"127.0.0.1","remotePort":4526},"msg":"incoming request"}
{"level":30,"time":1759026089527,"pid":22624,"hostname":"Velen","reqId":"req-6","res":{"statusCode":200},"responseTime":0.687000036239624,"msg":"request completed"}
{"level":30,"time":1759026092367,"pid":22624,"hostname":"Velen","reqId":"req-5","res":{"statusCode":200},"responseTime":2967.6523000001907,"msg":"request completed"}
{"level":30,"time":1759026412041,"pid":22624,"hostname":"Velen","reqId":"req-7","req":{"method":"POST","url":"/api/config","host":"127.0.0.1:2526","remoteAddress":"127.0.0.1","remotePort":14745},"msg":"incoming request"}
{"level":30,"time":1759026412049,"pid":22624,"hostname":"Velen","reqId":"req-7","res":{"statusCode":200},"responseTime":7.747099995613098,"msg":"request completed"}
{"level":30,"time":1759026412053,"pid":22624,"hostname":"Velen","reqId":"req-8","req":{"method":"POST","url":"/api/restart","host":"127.0.0.1:2526","remoteAddress":"127.0.0.1","remotePort":14745},"msg":"incoming request"}
{"level":30,"time":1759026412054,"pid":22624,"hostname":"Velen","reqId":"req-8","res":{"statusCode":200},"responseTime":0.8395999670028687,"msg":"request completed"}
