{"level":30,"time":1759021814722,"pid":44676,"hostname":"<PERSON><PERSON><PERSON>","msg":"register transformer: Anthropic (endpoint: /v1/messages)"}
{"level":30,"time":1759021814722,"pid":44676,"hostname":"<PERSON>ele<PERSON>","msg":"register transformer: gemini (endpoint: /v1beta/models/:modelAndAction)"}
{"level":30,"time":1759021814722,"pid":44676,"hostname":"<PERSON>ele<PERSON>","msg":"register transformer: vertex-gemini (no endpoint)"}
{"level":30,"time":1759021814722,"pid":44676,"hostname":"Velen","msg":"register transformer: vertex-claude (no endpoint)"}
{"level":30,"time":1759021814722,"pid":44676,"hostname":"<PERSON>ele<PERSON>","msg":"register transformer: deepseek (no endpoint)"}
{"level":30,"time":1759021814722,"pid":44676,"hostname":"<PERSON>ele<PERSON>","msg":"register transformer: tooluse (no endpoint)"}
{"level":30,"time":1759021814722,"pid":44676,"hostname":"Velen","msg":"register transformer: openrouter (no endpoint)"}
{"level":30,"time":1759021814722,"pid":44676,"hostname":"Velen","msg":"register transformer: OpenAI (endpoint: /v1/chat/completions)"}
{"level":30,"time":1759021814723,"pid":44676,"hostname":"Velen","msg":"register transformer: maxtoken (no endpoint)"}
{"level":30,"time":1759021814723,"pid":44676,"hostname":"Velen","msg":"register transformer: groq (no endpoint)"}
{"level":30,"time":1759021814723,"pid":44676,"hostname":"Velen","msg":"register transformer: cleancache (no endpoint)"}
{"level":30,"time":1759021814723,"pid":44676,"hostname":"Velen","msg":"register transformer: enhancetool (no endpoint)"}
{"level":30,"time":1759021814723,"pid":44676,"hostname":"Velen","msg":"register transformer: reasoning (no endpoint)"}
{"level":30,"time":1759021814723,"pid":44676,"hostname":"Velen","msg":"register transformer: sampling (no endpoint)"}
{"level":30,"time":1759021814723,"pid":44676,"hostname":"Velen","msg":"register transformer: maxcompletiontokens (no endpoint)"}
{"level":30,"time":1759021814723,"pid":44676,"hostname":"Velen","msg":"register transformer: cerebras (no endpoint)"}
{"level":30,"time":1759021814723,"pid":44676,"hostname":"Velen","msg":"register transformer: streamoptions (no endpoint)"}
{"level":30,"time":1759021814723,"pid":44676,"hostname":"Velen","msg":"register transformer: customparams (no endpoint)"}
{"level":30,"time":1759021814729,"pid":44676,"hostname":"Velen","msg":"Iflow provider registered"}
{"level":30,"time":1759021814729,"pid":44676,"hostname":"Velen","msg":"魔搭全能 provider registered"}
{"level":30,"time":1759021814729,"pid":44676,"hostname":"Velen","msg":"魔搭快速 provider registered"}
{"level":30,"time":1759021814729,"pid":44676,"hostname":"Velen","msg":"魔搭思考 provider registered"}
{"level":30,"time":1759021814729,"pid":44676,"hostname":"Velen","msg":"魔搭共享 provider registered"}
{"level":30,"time":1759021814729,"pid":44676,"hostname":"Velen","msg":"gemini-pro provider registered"}
{"level":30,"time":1759021814729,"pid":44676,"hostname":"Velen","msg":"gemini-flash provider registered"}
{"level":30,"time":1759021814729,"pid":44676,"hostname":"Velen","msg":"GeminiTier1 provider registered"}
{"level":30,"time":1759021814798,"pid":44676,"hostname":"Velen","msg":"Server listening at http://127.0.0.1:2526"}
{"level":30,"time":1759021814798,"pid":44676,"hostname":"Velen","msg":"🚀 LLMs API server listening on http://127.0.0.1:2526"}
{"level":30,"time":1759024441032,"pid":44676,"hostname":"Velen","reqId":"req-1","req":{"method":"POST","url":"/api/config","host":"127.0.0.1:2526","remoteAddress":"127.0.0.1","remotePort":8463},"msg":"incoming request"}
{"level":30,"time":1759024441047,"pid":44676,"hostname":"Velen","reqId":"req-1","res":{"statusCode":200},"responseTime":15.183300018310547,"msg":"request completed"}
{"level":30,"time":1759024441053,"pid":44676,"hostname":"Velen","reqId":"req-2","req":{"method":"POST","url":"/api/restart","host":"127.0.0.1:2526","remoteAddress":"127.0.0.1","remotePort":8463},"msg":"incoming request"}
{"level":30,"time":1759024441054,"pid":44676,"hostname":"Velen","reqId":"req-2","res":{"statusCode":200},"responseTime":1.159600019454956,"msg":"request completed"}
{"level":30,"time":1759024444027,"pid":44676,"hostname":"Velen","reqId":"req-3","req":{"method":"GET","url":"/ui/","host":"127.0.0.1:2526","remoteAddress":"127.0.0.1","remotePort":8463},"msg":"incoming request"}
{"level":30,"time":1759024444033,"pid":44676,"hostname":"Velen","reqId":"req-3","res":{"statusCode":304},"responseTime":6.330699980258942,"msg":"request completed"}
{"level":30,"time":1759024444205,"pid":44676,"hostname":"Velen","reqId":"req-4","req":{"method":"GET","url":"/api/config","host":"127.0.0.1:2526","remoteAddress":"127.0.0.1","remotePort":8463},"msg":"incoming request"}
{"level":30,"time":1759024444206,"pid":44676,"hostname":"Velen","reqId":"req-5","req":{"method":"GET","url":"/favicon.ico","host":"127.0.0.1:2526","remoteAddress":"127.0.0.1","remotePort":8505},"msg":"incoming request"}
{"level":30,"time":1759024444207,"pid":44676,"hostname":"Velen","reqId":"req-5","res":{"statusCode":401},"responseTime":0.673799991607666,"msg":"request completed"}
{"level":30,"time":1759024444210,"pid":44676,"hostname":"Velen","reqId":"req-4","res":{"statusCode":200},"responseTime":4.3841999769210815,"msg":"request completed"}
{"level":30,"time":1759024444233,"pid":44676,"hostname":"Velen","reqId":"req-6","req":{"method":"GET","url":"/api/config","host":"127.0.0.1:2526","remoteAddress":"127.0.0.1","remotePort":8463},"msg":"incoming request"}
{"level":30,"time":1759024444237,"pid":44676,"hostname":"Velen","reqId":"req-6","res":{"statusCode":200},"responseTime":3.627399981021881,"msg":"request completed"}
{"level":30,"time":1759024444238,"pid":44676,"hostname":"Velen","reqId":"req-7","req":{"method":"GET","url":"/api/update/check","host":"127.0.0.1:2526","remoteAddress":"127.0.0.1","remotePort":8505},"msg":"incoming request"}
{"level":30,"time":1759024444353,"pid":44676,"hostname":"Velen","reqId":"req-8","req":{"method":"GET","url":"/api/transformers","host":"127.0.0.1:2526","remoteAddress":"127.0.0.1","remotePort":8463},"msg":"incoming request"}
{"level":30,"time":1759024444354,"pid":44676,"hostname":"Velen","reqId":"req-8","res":{"statusCode":200},"responseTime":0.7681000232696533,"msg":"request completed"}
{"level":30,"time":1759024447002,"pid":44676,"hostname":"Velen","reqId":"req-7","res":{"statusCode":200},"responseTime":2764.034900009632,"msg":"request completed"}
