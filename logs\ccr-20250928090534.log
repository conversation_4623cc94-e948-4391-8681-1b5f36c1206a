{"level":30,"time":1759021534857,"pid":38096,"hostname":"<PERSON><PERSON><PERSON>","msg":"register transformer: Anthropic (endpoint: /v1/messages)"}
{"level":30,"time":1759021534857,"pid":38096,"hostname":"<PERSON><PERSON><PERSON>","msg":"register transformer: gemini (endpoint: /v1beta/models/:modelAndAction)"}
{"level":30,"time":1759021534857,"pid":38096,"hostname":"<PERSON>ele<PERSON>","msg":"register transformer: vertex-gemini (no endpoint)"}
{"level":30,"time":1759021534857,"pid":38096,"hostname":"Velen","msg":"register transformer: vertex-claude (no endpoint)"}
{"level":30,"time":1759021534857,"pid":38096,"hostname":"<PERSON><PERSON><PERSON>","msg":"register transformer: deepseek (no endpoint)"}
{"level":30,"time":1759021534857,"pid":38096,"hostname":"<PERSON><PERSON><PERSON>","msg":"register transformer: tooluse (no endpoint)"}
{"level":30,"time":1759021534857,"pid":38096,"hostname":"Velen","msg":"register transformer: openrouter (no endpoint)"}
{"level":30,"time":1759021534857,"pid":38096,"hostname":"Velen","msg":"register transformer: OpenAI (endpoint: /v1/chat/completions)"}
{"level":30,"time":1759021534857,"pid":38096,"hostname":"Velen","msg":"register transformer: maxtoken (no endpoint)"}
{"level":30,"time":1759021534857,"pid":38096,"hostname":"Velen","msg":"register transformer: groq (no endpoint)"}
{"level":30,"time":1759021534858,"pid":38096,"hostname":"Velen","msg":"register transformer: cleancache (no endpoint)"}
{"level":30,"time":1759021534858,"pid":38096,"hostname":"Velen","msg":"register transformer: enhancetool (no endpoint)"}
{"level":30,"time":1759021534858,"pid":38096,"hostname":"Velen","msg":"register transformer: reasoning (no endpoint)"}
{"level":30,"time":1759021534858,"pid":38096,"hostname":"Velen","msg":"register transformer: sampling (no endpoint)"}
{"level":30,"time":1759021534858,"pid":38096,"hostname":"Velen","msg":"register transformer: maxcompletiontokens (no endpoint)"}
{"level":30,"time":1759021534858,"pid":38096,"hostname":"Velen","msg":"register transformer: cerebras (no endpoint)"}
{"level":30,"time":1759021534858,"pid":38096,"hostname":"Velen","msg":"register transformer: streamoptions (no endpoint)"}
{"level":30,"time":1759021534858,"pid":38096,"hostname":"Velen","msg":"register transformer: customparams (no endpoint)"}
{"level":30,"time":1759021534864,"pid":38096,"hostname":"Velen","msg":"Iflow provider registered"}
{"level":30,"time":1759021534865,"pid":38096,"hostname":"Velen","msg":"魔搭全能 provider registered"}
{"level":30,"time":1759021534865,"pid":38096,"hostname":"Velen","msg":"魔搭快速 provider registered"}
{"level":30,"time":1759021534865,"pid":38096,"hostname":"Velen","msg":"魔搭思考 provider registered"}
{"level":30,"time":1759021534865,"pid":38096,"hostname":"Velen","msg":"魔搭共享 provider registered"}
{"level":30,"time":1759021534865,"pid":38096,"hostname":"Velen","msg":"gemini-pro provider registered"}
{"level":30,"time":1759021534865,"pid":38096,"hostname":"Velen","msg":"gemini-flash provider registered"}
{"level":30,"time":1759021534865,"pid":38096,"hostname":"Velen","msg":"GeminiTier1 provider registered"}
{"level":30,"time":1759021534925,"pid":38096,"hostname":"Velen","msg":"Server listening at http://127.0.0.1:2526"}
{"level":30,"time":1759021534925,"pid":38096,"hostname":"Velen","msg":"🚀 LLMs API server listening on http://127.0.0.1:2526"}
{"level":30,"time":1759021764221,"pid":38096,"hostname":"Velen","reqId":"req-1","req":{"method":"POST","url":"/api/config","host":"127.0.0.1:2526","remoteAddress":"127.0.0.1","remotePort":10084},"msg":"incoming request"}
{"level":30,"time":1759021764238,"pid":38096,"hostname":"Velen","reqId":"req-1","res":{"statusCode":200},"responseTime":16.35089999437332,"msg":"request completed"}
{"level":30,"time":1759021764246,"pid":38096,"hostname":"Velen","reqId":"req-2","req":{"method":"POST","url":"/api/restart","host":"127.0.0.1:2526","remoteAddress":"127.0.0.1","remotePort":10084},"msg":"incoming request"}
{"level":30,"time":1759021764247,"pid":38096,"hostname":"Velen","reqId":"req-2","res":{"statusCode":200},"responseTime":1.0540000200271606,"msg":"request completed"}
