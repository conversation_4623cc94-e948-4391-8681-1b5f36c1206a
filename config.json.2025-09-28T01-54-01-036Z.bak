{"LOG": true, "LOG_LEVEL": "info", "CLAUDE_PATH": "", "HOST": "127.0.0.1", "PORT": 2526, "APIKEY": "19891025", "API_TIMEOUT_MS": "600000", "PROXY_URL": "", "transformers": [], "Providers": [{"name": "Iflow", "api_base_url": "http://127.0.0.1:3001/proxy/iflow/v1/chat/completions", "api_key": "sk-CgswUenjg4NTnB6MBGStv5kGnAAJY-ly3y4G6qA-a_Wvdq_Y", "models": ["qwen3-max-preview", "kimi-k2-0905", "qwen3-coder"], "transformer": {"use": []}}, {"name": "魔搭全能", "api_base_url": "http://127.0.0.1:3001/proxy/msall/v1/chat/completions", "api_key": "sk-lpd32PGo-mRgQizQrnRhjED_aHgCRkb-CI3GHXpBy4-pju-d", "models": ["ZhipuAI/GLM-4.5", "Qwen/Qwen3-Coder-480B-A35B-Instruct"], "transformer": {"use": [], "Qwen/Qwen3-Coder-480B-A35B-Instruct": {"use": ["enhancetool"]}}}, {"name": "魔搭快速", "api_base_url": "http://127.0.0.1:3001/proxy/msspeed/v1/chat/completions", "api_key": "sk-19zyc3ajKhUuCuQW80nDIXqi4_5kYHRHW_uWD9FguPy7NyZH", "models": ["Qwen/Qwen3-Next-80B-A3B-Instruct", "Qwen/Qwen3-235B-A22B-Instruct-2507"], "transformer": {"use": [], "Qwen/Qwen3-Next-80B-A3B-Instruct": {"use": ["enhancetool"]}, "Qwen/Qwen3-235B-A22B-Instruct-2507": {"use": ["enhancetool"]}}}, {"name": "魔搭思考", "api_base_url": "http://127.0.0.1:3001/proxy/msthinking/v1/chat/completions", "api_key": "sk-fd2hrbUJVdVk4BBM7MNARLJMhHh-Ayq9c9ac3xqn608B3g6I", "models": ["Qwen/Qwen3-235B-A22B-Thinking-2507", "Qwen/Qwen3-Next-80B-A3B-Thinking"], "transformer": {"use": [["maxtoken", {"max_tokens": 65536}], "enhancetool"], "Qwen/Qwen3-235B-A22B-Thinking-2507": {"use": ["reasoning"]}, "Qwen/Qwen3-Next-80B-A3B-Thinking": {"use": ["reasoning"]}}}, {"name": "魔搭共享", "api_base_url": "http://127.0.0.1:3001/proxy/msshare/v1/chat/completions", "api_key": "sk-fd2hrbUJVdVk4BBM7MNARLJMhHh-Ayq9c9ac3xqn608B3g6I", "models": [], "transformer": {"use": [], "Qwen/Qwen3-Coder-480B-A35B-Instruct": {"use": ["enhancetool"]}, "Qwen/Qwen3-235B-A22B-Thinking-2507": {"use": ["reasoning"]}, "Qwen/Qwen2.5-VL-72B-Instruct": {"use": ["maxcomple<PERSON><PERSON><PERSON>"]}}}, {"name": "gemini-pro", "api_base_url": "http://127.0.0.1:3001/proxy/gemini-pro/v1beta/models/", "api_key": "sk-7UTRrpB-tjTzKvnwe1V-WNHLnqUHWOWH0Hg6s4SPeOCZo4yd", "models": ["gemini-2.5-pro"], "transformer": {"use": ["gemini"]}}, {"name": "gemini-flash", "api_base_url": "http://127.0.0.1:3001/proxy/gemini-flash/v1beta/models/", "api_key": "sk-Q9H6s-05qNj8sT9Vb-vM3FEdOR8y8M_uf4zEASZNqAoYPY_t", "models": ["gemini-2.5-flash"], "transformer": {"use": ["gemini"]}}, {"name": "GeminiTier1", "api_base_url": "http://127.0.0.1:3001/proxy/gemini-tier1/v1beta/models/", "api_key": "sk-4vq-WOGe_tp0JSn2yACnjfFRpvZb7Uun6aCUed8h7ddr9B-E", "models": ["gemini-2.5-pro", "gemini-2.5-flash", "gemini-2.5-flash-lite", "gemini-2.5-flash-image-preview", "gemini-2.5-pro-preview-tts", "gemini-2.5-flash-preview-tts"], "transformer": {"use": ["gemini"]}}], "StatusLine": {"enabled": false, "currentStyle": "default", "default": {"modules": []}, "powerline": {"modules": [{"type": "model", "icon": "🤖", "text": "{{model}}", "color": "bright_yellow"}, {"type": "usage", "icon": "📊", "text": "{{inputTokens}} → {{outputTokens}}", "color": "bright_magenta"}]}}, "Router": {"default": "GeminiTier1,gemini-2.5-flash", "background": "GeminiTier1,gemini-2.5-flash-lite", "think": "GeminiTier1,gemini-2.5-pro", "longContext": "GeminiTier1,gemini-2.5-pro", "longContextThreshold": 250000, "webSearch": "GeminiTier1,gemini-2.5-flash", "image": "GeminiTier1,gemini-2.5-flash"}, "CUSTOM_ROUTER_PATH": "C:/Users/<USER>/.claude-code-router/velen_router.js"}