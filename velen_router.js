/**
 * <PERSON><PERSON><PERSON> 生产级AI智能路由器 - 完全依赖CCR机制
 *
 * 核心设计理念：
 * 1. 完全依赖CCR的现有机制（token计算、上下文压缩等）
 * 2. 后台模型直接返回路由标识进行精准路由
 * 3. 实现上下文压缩后>256k的flash总结机制
 * 4. 零重复功能，充分利用CCR基础设施
 *
 * <AUTHOR>
 * @version 10.0 (Production Ready)
 * @param {object} req - The request object from Claude Code, containing the request body.
 * @param {object} config - The application's config object.
 * @returns {Promise<string|null>} - A promise that resolves to the "provider,model_name" string, or null to use the default router.
 */



/**
 * 基于LMArena排行榜数据和复杂度分级的智能路由映射表
 *
 * 13个任务类型的三级复杂度分级策略：
 * 1. reasoning: 简单(Next-Thinking) → 中等(235B-Thinking) → 复杂(Gemini Pro)
 * 2. math: 简单(Next-Instruct) → 中等(qwen3-max) → 复杂(Gemini Pro)
 * 3. instruction-following: 简单(235B-Instruct) → 中等(qwen3-max) → 复杂(Gemini Pro)
 * 4. multi-turn: 简单(235B-Instruct) → 中等(qwen3-max) → 复杂(Gemini Pro)
 * 5. creative-writing: 简单(qwen3-max) → 中等(Gemini Flash) → 复杂(Gemini Pro)
 * 6. coding: 简单(Next-Instruct) → 中等(235B-Instruct) → 复杂(qwen3-max)
 * 7. hard-prompts: 简单(235B-Instruct) → 中等(qwen3-max) → 复杂(Gemini Pro)
 * 8. longer-query: 简单(235B-Instruct) → 中等(qwen3-max) → 复杂(Gemini Pro)
 * 9. webdev: 简单(Coder-480B) → 中等(GLM-4.5) → 复杂(Gemini Pro)
 * 10. vision: 简单(Gemini Flash) → 复杂(Gemini Pro)
 * 11. text-to-image: gemini-2.5-flash-image-preview
 * 12. image-edit: gemini-2.5-flash-image-preview
 * 13. search: gemini-2.5-pro
 */
const TASK_COMPLEXITY_MAPPING = {
  // 1. 推理任务 - 基于Hard Prompts排行榜
  'reasoning': {
    simple: '魔搭思考,Qwen/Qwen3-Next-80B-A3B-Thinking',        // 简单推理
    medium: '魔搭思考,Qwen/Qwen3-235B-A22B-Thinking-2507',      // 中等推理
    complex: 'GeminiTier1,gemini-2.5-pro'                       // 复杂推理
  },

  // 2. 数学任务 - 基于Math排行榜
  'math': {
    simple: '魔搭快速,Qwen/Qwen3-Next-80B-A3B-Instruct',        // 简单数学
    medium: 'Iflow,qwen3-max-preview',                           // 中等数学
    complex: 'GeminiTier1,gemini-2.5-pro'                       // 复杂数学
  },

  // 3. 指令跟随 - 基于Instruction Following排行榜
  'instruction-following': {
    simple: '魔搭全能,Qwen/Qwen3-235B-A22B-Instruct-2507',      // 简单指令
    medium: 'Iflow,qwen3-max-preview',                           // 中等指令
    complex: 'GeminiTier1,gemini-2.5-pro'                       // 复杂指令
  },

  // 4. 多轮对话 - 基于Multi-turn排行榜
  'multi-turn': {
    simple: '魔搭全能,Qwen/Qwen3-235B-A22B-Instruct-2507',      // 简单对话
    medium: 'Iflow,qwen3-max-preview',                           // 中等对话
    complex: 'GeminiTier1,gemini-2.5-pro'                       // 复杂对话
  },

  // 5. 创意写作 - 基于Creative Writing排行榜
  'creative-writing': {
    simple: 'Iflow,qwen3-max-preview',                           // 简单创意
    medium: 'GeminiTier1,gemini-2.5-flash',                     // 中等创意
    complex: 'GeminiTier1,gemini-2.5-pro'                       // 复杂创意
  },

  // 6. 编程任务 - 基于Coding排行榜
  'coding': {
    simple: '魔搭快速,Qwen/Qwen3-Next-80B-A3B-Instruct',        // 简单编程
    medium: '魔搭全能,Qwen/Qwen3-235B-A22B-Instruct-2507',      // 中等编程
    complex: 'Iflow,qwen3-max-preview'                           // 复杂编程
  },

  // 7. 困难提示 - 基于Hard Prompts排行榜
  'hard-prompts': {
    simple: '魔搭全能,Qwen/Qwen3-235B-A22B-Instruct-2507',      // 简单困难提示
    medium: 'Iflow,qwen3-max-preview',                           // 中等困难提示
    complex: 'GeminiTier1,gemini-2.5-pro'                       // 复杂困难提示
  },

  // 8. 长查询 - 基于Longer Query排行榜
  'longer-query': {
    simple: '魔搭全能,Qwen/Qwen3-235B-A22B-Instruct-2507',      // 简单长查询
    medium: 'Iflow,qwen3-max-preview',                           // 中等长查询
    complex: 'GeminiTier1,gemini-2.5-pro'                       // 复杂长查询
  },

  // 9. Web开发 - 基于WebDev排行榜
  'webdev': {
    simple: '魔搭全能,Qwen/Qwen3-Coder-480B-A35B-Instruct',     // 简单Web开发
    medium: '魔搭全能,ZhipuAI/GLM-4.5',                         // 中等Web开发
    complex: 'GeminiTier1,gemini-2.5-pro'                       // 复杂Web开发
  },

  // 10. 视觉任务 - 基于Vision排行榜
  'vision': {
    simple: 'GeminiTier1,gemini-2.5-flash',                     // 简单视觉
    complex: 'GeminiTier1,gemini-2.5-pro'                       // 复杂视觉
  },

  // 11-13. 专用任务
  'text-to-image': 'GeminiTier1,gemini-2.5-flash-image-preview',  // 文本转图像
  'image-edit': 'GeminiTier1,gemini-2.5-flash-image-preview',     // 图像编辑
  'search': 'GeminiTier1,gemini-2.5-pro'                          // 搜索任务
};

/**
 * AI路由提示词 - 基于13个LMArena任务类型的智能分析
 * 返回三个变量：taskType(任务类型)、complexity(复杂度)、context(上下文需求)
 */
const AI_ROUTING_PROMPT = `你是一个专业的AI路由分析器，基于LMArena竞技场的13个任务类型进行分析。

请分析用户请求，返回JSON格式的路由决策。

13个LMArena任务类型：
1. reasoning: 推理任务 - 逻辑推理、哲学思考、复杂分析
2. math: 数学任务 - 数学计算、公式推导、数学证明
3. instruction-following: 指令跟随 - 按照具体指令执行任务
4. multi-turn: 多轮对话 - 需要上下文的连续对话
5. creative-writing: 创意写作 - 故事创作、诗歌、创意内容
6. coding: 编程任务 - 代码编写、算法实现、程序设计
7. hard-prompts: 困难提示 - 复杂、模糊或困难的任务
8. longer-query: 长查询 - 需要处理长文本或复杂查询
9. webdev: Web开发 - 前端、后端、全栈Web开发
10. vision: 视觉任务 - 图像分析、视觉理解(如果有图像)
11. text-to-image: 文本转图像 - 图像生成需求
12. image-edit: 图像编辑 - 图像修改、处理需求
13. search: 搜索任务 - 信息检索、搜索相关

复杂度评分标准(1-10分)：
- 1-3分: 简单任务(基础问答、简单计算、直接指令)
- 4-6分: 中等任务(需要一定思考、多步骤、中等难度)
- 7-10分: 复杂任务(深度思考、专业知识、多重推理)

上下文需求：
- low: 简单任务，上下文需求低
- medium: 中等任务，需要一定上下文
- high: 复杂任务，需要大量上下文

请返回JSON格式：{"taskType": "任务类型", "complexity": 复杂度数字, "context": "上下文需求"}

用户请求：`;

/**
 * 基于任务类型和复杂度选择最优模型
 * @param {string} taskType - LMArena任务类型
 * @param {number} complexity - 复杂度(1-10)
 * @returns {string} - 选择的模型
 */
function selectModelByComplexity(taskType, complexity) {
  console.log(`🎯 [Model Selection] 任务类型: ${taskType}, 复杂度: ${complexity}`);

  const mapping = TASK_COMPLEXITY_MAPPING[taskType];
  if (!mapping) {
    console.log(`⚠️ [Model Selection] 未知任务类型: ${taskType}, 使用默认策略`);
    return complexity >= 7 ? 'GeminiTier1,gemini-2.5-pro' : 'GeminiTier1,gemini-2.5-flash';
  }

  // 专用任务(无复杂度分级)
  if (typeof mapping === 'string') {
    console.log(`🎯 [Model Selection] 专用任务模型: ${mapping}`);
    return mapping;
  }

  // 有复杂度分级的任务
  let selectedModel;
  if (complexity <= 3) {
    selectedModel = mapping.simple;
    console.log(`🟢 [Model Selection] 简单任务(${complexity}分) → ${selectedModel}`);
  } else if (complexity <= 6) {
    selectedModel = mapping.medium || mapping.simple;
    console.log(`🟡 [Model Selection] 中等任务(${complexity}分) → ${selectedModel}`);
  } else {
    selectedModel = mapping.complex || mapping.medium || mapping.simple;
    console.log(`🔴 [Model Selection] 复杂任务(${complexity}分) → ${selectedModel}`);
  }

  return selectedModel;
}

/**
 * 分层上下文处理函数 - 基于AI预处理结果进行模型升级
 * @param {number} tokenCount - token数量
 * @param {string} selectedModel - AI预处理选择的基础模型
 * @param {number} complexity - 任务复杂度
 * @returns {string|null} - 升级后的模型或null（使用原模型）
 */
function handleLayeredContext(tokenCount, selectedModel, complexity) {
  console.log(`🏗️ [Layered Context] 开始分层上下文处理: ${tokenCount.toLocaleString()} tokens, 基础模型=${selectedModel}, complexity=${complexity}`);

  // 小于120k tokens，不需要升级
  if (tokenCount < 120000) {
    console.log(`📊 [Normal] <120k tokens，使用AI预处理选择的模型`);
    return null; // 返回null表示使用原始的AI预处理结果
  }

  // 第一层：120k-250k tokens - 轻微升级策略
  if (tokenCount >= 120000 && tokenCount <= 250000) {
    console.log(`📊 [Layer 1] 120k-250k tokens范围，考虑轻微升级`);
    // 对于中等上下文，如果复杂度高，升级到更强模型
    if (complexity >= 7) {
      console.log(`📊 [Layer 1] 高复杂度任务，升级到Pro模型`);
      return 'GeminiTier1,gemini-2.5-pro';
    }
    return null; // 使用AI预处理的结果
  }

  // 第二层：250k-1M tokens - 智能升级策略
  if (tokenCount > 250000 && tokenCount <= 1000000) {
    console.log(`📊 [Layer 2] 250k-1M tokens范围，执行智能升级`);
    // 大上下文任务，根据复杂度选择升级策略
    if (complexity >= 6) {
      console.log(`📊 [Layer 2] 中高复杂度任务，升级到Pro模型`);
      return 'GeminiTier1,gemini-2.5-pro';
    } else {
      console.log(`📊 [Layer 2] 中低复杂度任务，升级到Flash模型`);
      return 'GeminiTier1,gemini-2.5-flash';
    }
  }

  // 第三层：1M-2M tokens - 强制Pro策略
  if (tokenCount > 1000000 && tokenCount <= 2000000) {
    console.log(`📊 [Layer 3] 1M-2M tokens(${tokenCount.toLocaleString()})，强制升级到Pro模型`);
    return 'GeminiTier1,gemini-2.5-pro';
  }

  // 第四层：>2M tokens - 使用最强模型处理超大上下文
  if (tokenCount > 2000000) {
    console.log(`📊 [Layer 4] >2M tokens(${tokenCount.toLocaleString()})，使用最强模型处理超大上下文`);
    return 'GeminiTier1,gemini-2.5-pro';
  }

  return null; // 默认使用AI预处理的结果
}

/**
 * 使用HTTP请求直接调用后台模型进行AI路由决策 - 双变量返回机制
 * 返回路由标识和上下文需求两个变量
 *
 * @param {object} requestBody - 完整的请求体，包含messages、system、tools、thinking等所有字段
 * @param {object} config - 配置对象
 */
async function callBackendModelForRouting(requestBody, config) {
  const backgroundModel = config.Router?.background || 'GeminiTier1,gemini-2.5-flash-lite';
  console.log(`🤖 [VELEN AI ROUTER] ===== 预处理模型调用 =====`);
  console.log(`🤖 [VELEN AI ROUTER] 预处理使用后台模型: ${backgroundModel}`);
  console.log(`🤖 [VELEN AI ROUTER] 注意：这是预处理分析模型，不是最终执行模型`);

  // 解析后台模型配置
  const [providerName, modelName] = backgroundModel.split(',');
  const provider = config.Providers?.find(p => p.name === providerName);

  if (!provider) {
    console.error(`❌ [Clean AI Router] 未找到Provider: ${providerName}`);
    return null;
  }

  // 提取所有相关信息用于路由决策
  const analysisData = {
    messages: requestBody.messages || [],
    system: requestBody.system || [],
    tools: requestBody.tools || [],
    thinking: requestBody.thinking || false,
    metadata: requestBody.metadata || {},
    model: requestBody.model || 'unknown'
  };

  console.log(`📋 [Clean AI Router] 分析数据概览:`);
  console.log(`   - 对话消息: ${analysisData.messages.length}条`);
  console.log(`   - 系统消息: ${analysisData.system.length}条`);
  console.log(`   - 工具调用: ${analysisData.tools.length}个`);
  console.log(`   - 思考模式: ${analysisData.thinking ? '启用' : '未启用'}`);
  console.log(`   - 请求模型: ${analysisData.model}`);

  // 构建发送给后台模型的消息
  const routingMessages = [
    {
      role: 'system',
      content: AI_ROUTING_PROMPT
    },
    {
      role: 'user',
      content: `请分析以下完整请求并返回路由决策：\n\n${JSON.stringify(analysisData, null, 2)}`
    }
  ];

  try {
    // 构建API URL和请求体
    let apiUrl = provider.api_base_url;
    let requestBody;

    // 检查是否是Gemini API（需要特殊处理）
    if (provider.api_base_url.includes('/v1beta/models/')) {
      // Gemini API格式：URL + 模型名 + :generateContent
      apiUrl = `${provider.api_base_url}${modelName}:generateContent`;
      requestBody = {
        contents: routingMessages.map(msg => ({
          role: msg.role === 'system' ? 'user' : msg.role,
          parts: [{ text: msg.content }]
        })),
        generationConfig: {
          maxOutputTokens: 100,
          temperature: 0.1
        }
      };
    } else {
      // 标准OpenAI API格式
      requestBody = {
        model: modelName,
        messages: routingMessages,
        max_tokens: 100,
        temperature: 0.1
      };
    }

    console.log(`🌐 [Clean AI Router] 调用API: ${apiUrl}`);
    console.log(`📋 [Clean AI Router] 请求体: ${JSON.stringify(requestBody, null, 2)}`);

    // 使用fetch进行HTTP请求
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${provider.api_key}`
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const responseData = await response.json();
    console.log(`📥 [Clean AI Router] API响应: ${JSON.stringify(responseData, null, 2)}`);

    // 解析响应内容
    let responseContent = '';
    if (responseData.candidates && responseData.candidates[0] && responseData.candidates[0].content) {
      // Gemini API响应格式
      responseContent = responseData.candidates[0].content.parts[0].text;
    } else if (responseData.choices && responseData.choices[0] && responseData.choices[0].message) {
      // OpenAI API响应格式
      responseContent = responseData.choices[0].message.content;
    } else if (responseData.content) {
      // 其他格式
      responseContent = responseData.content;
    } else {
      throw new Error('无法解析API响应格式');
    }

    console.log(`🎯 [Clean AI Router] 后台模型返回内容: ${responseContent}`);

    // 尝试解析JSON格式的三变量返回 (taskType, complexity, context)
    try {
      // 处理markdown格式的JSON响应
      let cleanedContent = responseContent.trim();
      if (responseContent.includes('```json')) {
        const jsonMatch = responseContent.match(/```json\s*\n?([\s\S]*?)\n?```/);
        if (jsonMatch) {
          cleanedContent = jsonMatch[1].trim();
        }
      }

      const routingResult = JSON.parse(cleanedContent);
      console.log(`✅ [Clean AI Router] JSON解析成功: taskType=${routingResult.taskType}, complexity=${routingResult.complexity}, context=${routingResult.context}`);

      // 基于任务类型和复杂度选择最优模型
      const selectedModel = selectModelByComplexity(routingResult.taskType, routingResult.complexity);

      console.log(`🎯 [VELEN AI ROUTER] ===== 预处理分析完成 =====`);
      console.log(`🎯 [VELEN AI ROUTER] 预处理结果: 任务类型=${routingResult.taskType}, 复杂度=${routingResult.complexity}`);
      console.log(`🎯 [VELEN AI ROUTER] 最终选择执行模型: ${selectedModel}`);
      console.log(`🎯 [VELEN AI ROUTER] 注意：${selectedModel} 是最终执行模型，不是预处理模型`);

      return {
        route: routingResult.taskType,
        needsContext: routingResult.context === 'high' || routingResult.context === true,
        complexity: routingResult.complexity || 5,
        selectedModel: selectedModel
      };
    } catch (parseError) {
      console.log(`⚠️ [Clean AI Router] JSON解析失败，尝试文本解析: ${parseError.message}`);

      // 如果JSON解析失败，尝试从文本中提取任务类型
      const content = responseContent.trim().toLowerCase();
      let taskType = 'instruction-following'; // 默认指令跟随
      let needsContext = false;
      let complexity = 5; // 默认中等复杂度

      // 尝试提取复杂度
      const complexityMatch = responseContent.match(/"complexity":\s*(\d+)/);
      if (complexityMatch) {
        complexity = parseInt(complexityMatch[1]);
      }

      // 简单的文本匹配来提取13个LMArena任务类型
      if (content.includes('reasoning')) {
        taskType = 'reasoning';
        needsContext = true;
      } else if (content.includes('math')) {
        taskType = 'math';
        needsContext = false;
      } else if (content.includes('instruction-following')) {
        taskType = 'instruction-following';
        needsContext = false;
      } else if (content.includes('multi-turn')) {
        taskType = 'multi-turn';
        needsContext = true;
      } else if (content.includes('creative-writing')) {
        taskType = 'creative-writing';
        needsContext = false;
      } else if (content.includes('coding')) {
        taskType = 'coding';
        needsContext = false;
      } else if (content.includes('hard-prompts')) {
        taskType = 'hard-prompts';
        needsContext = true;
      } else if (content.includes('longer-query')) {
        taskType = 'longer-query';
        needsContext = true;
      } else if (content.includes('webdev')) {
        taskType = 'webdev';
        needsContext = false;
      } else if (content.includes('vision')) {
        taskType = 'vision';
        needsContext = false;
      } else if (content.includes('text-to-image')) {
        taskType = 'text-to-image';
        needsContext = false;
      } else if (content.includes('image-edit')) {
        taskType = 'image-edit';
        needsContext = false;
      } else if (content.includes('search')) {
        taskType = 'search';
        needsContext = false;
      }

      // 基于任务类型和复杂度选择最优模型
      const selectedModel = selectModelByComplexity(taskType, complexity);

      console.log(`🎯 [VELEN AI ROUTER] ===== 预处理分析完成(文本解析) =====`);
      console.log(`🎯 [VELEN AI ROUTER] 预处理结果: 任务类型=${taskType}, 复杂度=${complexity}`);
      console.log(`🎯 [VELEN AI ROUTER] 最终选择执行模型: ${selectedModel}`);
      console.log(`🎯 [VELEN AI ROUTER] 注意：${selectedModel} 是最终执行模型，不是预处理模型`);

      return {
        route: taskType,
        needsContext: needsContext,
        complexity: complexity,
        selectedModel: selectedModel
      };
    }

  } catch (error) {
    console.error(`❌ [Clean AI Router] 后台模型HTTP调用失败: ${error.message}`);
    return null;
  }
}

module.exports = async function router(req, config) {
  // 强制日志输出，确保路由器被调用
  console.error(`🚨 [VELEN ROUTER DEBUG] 自定义路由器被调用！时间: ${new Date().toISOString()}`);
  console.error(`🚨 [VELEN ROUTER DEBUG] 请求模型: ${req.body?.model || 'unknown'}`);
  console.error(`🚨 [VELEN ROUTER DEBUG] 配置的后台模型: ${config.Router?.background || 'undefined'}`);

  try {
    console.log(`🔄 [VELEN AI ROUTER] ========== 开始Velen智能路由决策 ==========`);
    console.log(`📋 [VELEN AI ROUTER] 请求模型: ${req.body?.model || 'unknown'}`);
    console.log(`📋 [VELEN AI ROUTER] 消息数量: ${req.body.messages?.length || 0}`);
    console.log(`📋 [VELEN AI ROUTER] 自定义路由器已激活，开始AI预处理分析`);

    // === 防并发控制 ===
    const now = Date.now();
    const timeSinceLastRequest = now - (module.exports.lastRequestTime || 0);
    
    if (timeSinceLastRequest < 200) {
      const delay = 200 - timeSinceLastRequest;
      console.log(`⏱️ [Clean AI Router] 防并发延迟: ${delay}ms`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }

    module.exports.lastRequestTime = Date.now();

    // === 1. 基础上下文检查 ===
    const tokenCount = req.tokenCount || 0;
    console.log(`📊 [Clean AI Router] Token数量: ${tokenCount.toLocaleString()}`);

    // 对于极小的请求，直接进行正常路由
    if (tokenCount < 120000) {
      console.log(`📊 [Clean AI Router] 小型请求(<120k tokens)，使用正常路由流程`);
      // 继续执行正常路由流程
    } else {
      console.log(`📊 [Clean AI Router] 大型请求(≥120k tokens)，需要分层处理`);
      // 大型请求需要先获取路由和复杂度信息，然后进行分层处理
    }

    // === 2. 检查AI路由配置 ===
    if (config.Router?.enableAIRouting === false) {
      console.log(`⚠️ [Clean AI Router] AI路由已禁用，使用默认路由`);
      return null;
    }

    if (!config.Router?.background) {
      console.log(`⚠️ [Clean AI Router] 未配置后台模型，使用默认路由`);
      return null;
    }

    // === 3. 获取请求内容信息 ===
    const messages = req.body.messages || [];
    const system = req.body.system || [];
    const tools = req.body.tools || [];

    console.log(`📋 [Clean AI Router] 请求内容概览:`);
    console.log(`   - 对话消息: ${messages.length}条`);
    console.log(`   - 系统消息: ${system.length}条`);
    console.log(`   - 工具调用: ${tools.length}个`);
    console.log(`   - 思考模式: ${req.body.thinking ? '启用' : '未启用'}`);

    // === 4. 调用后台模型进行路由决策（三变量返回） ===
    console.log(`🎯 [Clean AI Router] 调用后台模型进行完整请求分析`);
    const routingResult = await callBackendModelForRouting(req.body, config);

    if (!routingResult) {
      console.log(`❌ [Clean AI Router] 后台模型调用失败，返回null使用CCR默认处理`);
      return null;
    }

    const { route, needsContext, complexity, selectedModel } = routingResult;
    console.log(`🎯 [Clean AI Router] 路由结果: ${route}(${complexity}/10) → ${selectedModel} [${needsContext ? '完整上下文' : '当前消息'}]`);

    // === 5. 分层上下文处理 ===
    // 基于AI预处理结果，根据token数量进行模型升级
    console.log(`🏗️ [VELEN AI ROUTER] 检查是否需要基于上下文长度升级模型`);
    const layeredResult = handleLayeredContext(tokenCount, selectedModel, complexity);

    if (layeredResult) {
      console.log(`✅ [VELEN AI ROUTER] 模型升级完成: ${selectedModel} → ${layeredResult}`);
      console.log(`📊 [VELEN AI ROUTER] 预处理模型: ${config.Router?.background || 'GeminiTier1,gemini-2.5-flash-lite'}`);
      console.log(`📊 [VELEN AI ROUTER] AI选择基础模型: ${selectedModel}`);
      console.log(`📊 [VELEN AI ROUTER] 最终升级模型: ${layeredResult}`);
      console.log(`📊 [VELEN AI ROUTER] 注意：预处理→基础选择→上下文升级，三个阶段！`);
      return layeredResult;
    }
    // 如果分层处理返回null，使用AI预处理选择的模型

    // === 6. 返回选择的模型 ===
    if (selectedModel) {
      console.log(`✅ [VELEN AI ROUTER] ========== 路由决策完成 ==========`);
      console.log(`✅ [VELEN AI ROUTER] 最终执行模型: ${selectedModel}`);
      console.log(`📊 [VELEN AI ROUTER] 任务复杂度: ${complexity}/10, Token数量: ${tokenCount.toLocaleString()}`);
      console.log(`📊 [VELEN AI ROUTER] 预处理模型: ${config.Router?.background || 'GeminiTier1,gemini-2.5-flash-lite'}`);
      console.log(`📊 [VELEN AI ROUTER] 注意：预处理和执行是两个不同的模型！`);
      return selectedModel;
    } else {
      console.log(`⚠️ [VELEN AI ROUTER] 未找到匹配的模型，返回null使用CCR默认处理`);
      return null;
    }

  } catch (error) {
    console.error(`❌ [VELEN AI ROUTER] 路由处理错误:`, error);
    // 错误情况下使用安全的fallback模型，而不是返回null
    const safeModel = 'GeminiTier1,gemini-2.5-flash';
    console.log(`🔄 [VELEN AI ROUTER] 错误fallback路由到: ${safeModel}`);
    console.log(`📊 [VELEN AI ROUTER] 预处理模型: ${config.Router?.background || 'GeminiTier1,gemini-2.5-flash-lite'}`);
    console.log(`📊 [VELEN AI ROUTER] 注意：预处理正常，只是最终模型选择出错`);
    return safeModel;
  }
};
