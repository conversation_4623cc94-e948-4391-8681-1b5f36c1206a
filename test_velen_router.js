/**
 * Velen AI智能路由器端到端测试脚本
 * 验证三阶段逻辑流程：预处理→AI路由决策→分层上下文升级
 */

const fs = require('fs');
const path = require('path');

// 导入路由器和配置
const router = require('./velen_router.js');
const config = JSON.parse(fs.readFileSync('./config.json', 'utf8'));

// 测试用例定义
const testCases = [
  {
    name: "小型简单请求测试",
    description: "测试<120k tokens的简单指令跟随任务",
    request: {
      body: {
        model: "test-model",
        messages: [
          { role: "user", content: "请帮我写一个简单的Python函数来计算两个数的和" }
        ],
        system: [],
        tools: [],
        thinking: false
      },
      tokenCount: 50000 // 小于120k
    },
    expectedStage1: "预处理模型调用",
    expectedStage2: "instruction-following任务类型，复杂度1-3",
    expectedStage3: "使用AI预处理选择的基础模型"
  },
  {
    name: "中型复杂请求测试", 
    description: "测试120k-250k tokens的复杂编程任务",
    request: {
      body: {
        model: "test-model",
        messages: [
          { role: "user", content: "请设计一个完整的微服务架构，包括API网关、服务发现、负载均衡、数据库设计、缓存策略、监控告警等所有组件，并提供详细的实现代码和部署方案。需要考虑高可用、高并发、数据一致性等问题。" }
        ],
        system: [
          { type: "text", content: "你是一个资深的系统架构师，具有丰富的微服务设计经验" }
        ],
        tools: [
          { name: "code_generator", description: "代码生成工具" },
          { name: "architecture_designer", description: "架构设计工具" }
        ],
        thinking: true
      },
      tokenCount: 180000 // 120k-250k范围
    },
    expectedStage1: "预处理模型调用",
    expectedStage2: "coding任务类型，复杂度7-10",
    expectedStage3: "高复杂度任务升级到Pro模型"
  },
  {
    name: "大型超复杂请求测试",
    description: "测试>250k tokens的超复杂推理任务", 
    request: {
      body: {
        model: "test-model",
        messages: [
          { role: "user", content: "请深入分析量子计算对现代密码学的影响，包括Shor算法对RSA加密的威胁、量子密钥分发的原理、后量子密码学的发展方向、以及如何在现有系统中实现量子安全的迁移策略。需要从数学原理、技术实现、安全分析、产业应用等多个维度进行全面论述。" }
        ],
        system: [
          { type: "text", content: "你是量子计算和密码学领域的专家" }
        ],
        tools: [],
        thinking: true
      },
      tokenCount: 350000 // >250k
    },
    expectedStage1: "预处理模型调用",
    expectedStage2: "reasoning任务类型，复杂度8-10", 
    expectedStage3: "智能升级策略，复杂度≥6升级到Pro"
  },
  {
    name: "超大上下文请求测试",
    description: "测试>1M tokens的超大上下文任务",
    request: {
      body: {
        model: "test-model", 
        messages: [
          { role: "user", content: "分析这个大型代码库的架构问题" }
        ],
        system: [],
        tools: [],
        thinking: false
      },
      tokenCount: 1200000 // >1M
    },
    expectedStage1: "预处理模型调用",
    expectedStage2: "coding任务类型，复杂度5-7",
    expectedStage3: "强制升级到gemini-2.5-pro"
  }
];

// 测试结果记录
let testResults = [];

// 日志捕获函数
let capturedLogs = [];
const originalConsoleLog = console.log;
const originalConsoleError = console.error;

function startLogCapture() {
  capturedLogs = [];
  console.log = (...args) => {
    capturedLogs.push({ type: 'log', message: args.join(' '), timestamp: new Date().toISOString() });
    originalConsoleLog(...args);
  };
  console.error = (...args) => {
    capturedLogs.push({ type: 'error', message: args.join(' '), timestamp: new Date().toISOString() });
    originalConsoleError(...args);
  };
}

function stopLogCapture() {
  console.log = originalConsoleLog;
  console.error = originalConsoleError;
  return capturedLogs;
}

// 阶段验证函数
function validateStage1(logs) {
  const stage1Indicators = [
    '🚨 [VELEN ROUTER DEBUG] 自定义路由器被调用',
    '🤖 [VELEN AI ROUTER] ===== 预处理模型调用 =====',
    '🤖 [VELEN AI ROUTER] 预处理使用后台模型: GeminiTier1,gemini-2.5-flash-lite'
  ];
  
  const results = {
    routerCalled: false,
    preprocessingStarted: false,
    backgroundModelUsed: false,
    details: []
  };
  
  logs.forEach(log => {
    if (log.message.includes(stage1Indicators[0])) {
      results.routerCalled = true;
      results.details.push(`✅ 路由器被调用: ${log.timestamp}`);
    }
    if (log.message.includes(stage1Indicators[1])) {
      results.preprocessingStarted = true;
      results.details.push(`✅ 预处理开始: ${log.timestamp}`);
    }
    if (log.message.includes(stage1Indicators[2])) {
      results.backgroundModelUsed = true;
      results.details.push(`✅ 后台模型调用: ${log.timestamp}`);
    }
  });
  
  results.passed = results.routerCalled && results.preprocessingStarted && results.backgroundModelUsed;
  return results;
}

function validateStage2(logs) {
  const stage2Indicators = [
    '🎯 [VELEN AI ROUTER] ===== 预处理分析完成 =====',
    'taskType=',
    'complexity=',
    '最终选择执行模型:'
  ];
  
  const results = {
    analysisCompleted: false,
    taskTypeExtracted: false,
    complexityExtracted: false,
    modelSelected: false,
    taskType: null,
    complexity: null,
    selectedModel: null,
    details: []
  };
  
  logs.forEach(log => {
    if (log.message.includes(stage2Indicators[0])) {
      results.analysisCompleted = true;
      results.details.push(`✅ 预处理分析完成: ${log.timestamp}`);
    }
    if (log.message.includes(stage2Indicators[1])) {
      results.taskTypeExtracted = true;
      const match = log.message.match(/taskType=([^,\s]+)/);
      if (match) results.taskType = match[1];
      results.details.push(`✅ 任务类型提取: ${results.taskType}`);
    }
    if (log.message.includes(stage2Indicators[2])) {
      results.complexityExtracted = true;
      const match = log.message.match(/complexity=(\d+)/);
      if (match) results.complexity = parseInt(match[1]);
      results.details.push(`✅ 复杂度提取: ${results.complexity}`);
    }
    if (log.message.includes(stage2Indicators[3])) {
      results.modelSelected = true;
      const match = log.message.match(/最终选择执行模型:\s*([^\s]+)/);
      if (match) results.selectedModel = match[1];
      results.details.push(`✅ 模型选择: ${results.selectedModel}`);
    }
  });
  
  results.passed = results.analysisCompleted && results.taskTypeExtracted && 
                   results.complexityExtracted && results.modelSelected;
  return results;
}

function validateStage3(logs, tokenCount) {
  const stage3Indicators = [
    '🏗️ [VELEN AI ROUTER] 检查是否需要基于上下文长度升级模型',
    '✅ [VELEN AI ROUTER] 模型升级完成',
    '📊 [VELEN AI ROUTER] 预处理→基础选择→上下文升级，三个阶段'
  ];
  
  const results = {
    upgradeCheckStarted: false,
    upgradeCompleted: false,
    threeStagesShown: false,
    finalModel: null,
    upgradeReason: null,
    details: []
  };
  
  logs.forEach(log => {
    if (log.message.includes(stage3Indicators[0])) {
      results.upgradeCheckStarted = true;
      results.details.push(`✅ 升级检查开始: ${log.timestamp}`);
    }
    if (log.message.includes(stage3Indicators[1])) {
      results.upgradeCompleted = true;
      const match = log.message.match(/模型升级完成:\s*([^→]+)→\s*([^\s]+)/);
      if (match) {
        results.finalModel = match[2];
        results.details.push(`✅ 模型升级: ${match[1]} → ${match[2]}`);
      }
    }
    if (log.message.includes(stage3Indicators[2])) {
      results.threeStagesShown = true;
      results.details.push(`✅ 三阶段流程确认: ${log.timestamp}`);
    }
  });
  
  // 验证升级逻辑是否正确
  if (tokenCount < 120000) {
    results.expectedBehavior = "使用AI预处理选择的基础模型";
  } else if (tokenCount <= 250000) {
    results.expectedBehavior = "根据复杂度决定是否升级";
  } else if (tokenCount <= 1000000) {
    results.expectedBehavior = "智能升级策略";
  } else {
    results.expectedBehavior = "强制升级到Pro模型";
  }
  
  results.passed = results.upgradeCheckStarted;
  return results;
}

// 执行单个测试用例
async function runTestCase(testCase) {
  console.log(`\n🧪 开始测试: ${testCase.name}`);
  console.log(`📝 描述: ${testCase.description}`);
  
  startLogCapture();
  
  try {
    const result = await router(testCase.request, config);
    const logs = stopLogCapture();
    
    // 验证三个阶段
    const stage1Result = validateStage1(logs);
    const stage2Result = validateStage2(logs);
    const stage3Result = validateStage3(logs, testCase.request.tokenCount);
    
    const testResult = {
      testName: testCase.name,
      passed: stage1Result.passed && stage2Result.passed && stage3Result.passed,
      finalModel: result,
      stage1: stage1Result,
      stage2: stage2Result,
      stage3: stage3Result,
      logs: logs,
      timestamp: new Date().toISOString()
    };
    
    testResults.push(testResult);
    
    // 输出测试结果
    console.log(`\n📊 测试结果: ${testResult.passed ? '✅ 通过' : '❌ 失败'}`);
    console.log(`🎯 最终模型: ${result}`);
    console.log(`📋 阶段1 (预处理): ${stage1Result.passed ? '✅' : '❌'}`);
    console.log(`📋 阶段2 (AI决策): ${stage2Result.passed ? '✅' : '❌'}`);
    console.log(`📋 阶段3 (上下文升级): ${stage3Result.passed ? '✅' : '❌'}`);
    
    return testResult;
    
  } catch (error) {
    const logs = stopLogCapture();
    console.error(`❌ 测试执行失败: ${error.message}`);
    
    const testResult = {
      testName: testCase.name,
      passed: false,
      error: error.message,
      logs: logs,
      timestamp: new Date().toISOString()
    };
    
    testResults.push(testResult);
    return testResult;
  }
}

// 主测试函数
async function runAllTests() {
  console.log('🚀 开始Velen AI智能路由器端到端测试');
  console.log('🎯 测试目标: 验证三阶段逻辑流程');
  console.log(`📋 测试用例数量: ${testCases.length}`);
  
  for (const testCase of testCases) {
    await runTestCase(testCase);
    // 测试间隔，避免并发问题
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // 生成测试报告
  generateTestReport();
}

// 生成测试报告
function generateTestReport() {
  const passedTests = testResults.filter(r => r.passed).length;
  const totalTests = testResults.length;
  const passRate = ((passedTests / totalTests) * 100).toFixed(1);
  
  console.log('\n📊 ========== 测试报告 ==========');
  console.log(`📈 总体通过率: ${passedTests}/${totalTests} (${passRate}%)`);
  
  testResults.forEach(result => {
    console.log(`\n🧪 ${result.testName}: ${result.passed ? '✅ 通过' : '❌ 失败'}`);
    if (result.stage1) {
      console.log(`   阶段1 (预处理): ${result.stage1.passed ? '✅' : '❌'}`);
      result.stage1.details.forEach(detail => console.log(`     ${detail}`));
    }
    if (result.stage2) {
      console.log(`   阶段2 (AI决策): ${result.stage2.passed ? '✅' : '❌'}`);
      result.stage2.details.forEach(detail => console.log(`     ${detail}`));
    }
    if (result.stage3) {
      console.log(`   阶段3 (上下文升级): ${result.stage3.passed ? '✅' : '❌'}`);
      result.stage3.details.forEach(detail => console.log(`     ${detail}`));
    }
  });
  
  // 保存详细报告到文件
  const reportData = {
    summary: {
      totalTests,
      passedTests,
      passRate,
      timestamp: new Date().toISOString()
    },
    results: testResults
  };
  
  fs.writeFileSync('velen_router_test_report.json', JSON.stringify(reportData, null, 2));
  console.log('\n📄 详细测试报告已保存到: velen_router_test_report.json');
}

// 导出测试函数
module.exports = {
  runAllTests,
  runTestCase,
  testCases
};

// 如果直接运行此脚本
if (require.main === module) {
  runAllTests().catch(console.error);
}
