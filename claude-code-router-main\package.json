{"name": "@musistudio/claude-code-router", "version": "1.0.49", "description": "Use Claude Code without an Anthropics account and route it to another LLM provider", "bin": {"ccr": "./dist/cli.js"}, "scripts": {"build": "node scripts/build.js", "release": "npm run build && npm publish"}, "keywords": ["claude", "code", "router", "llm", "anthropic"], "author": "musistudio", "license": "MIT", "dependencies": {"@fastify/static": "^8.2.0", "@musistudio/llms": "^1.0.32", "dotenv": "^16.4.7", "find-process": "^2.0.0", "json5": "^2.2.3", "openurl": "^1.1.1", "rotating-file-stream": "^3.2.7", "tiktoken": "^1.0.21", "uuid": "^11.1.0"}, "devDependencies": {"@types/node": "^24.0.15", "esbuild": "^0.25.1", "fastify": "^5.4.0", "shx": "^0.4.0", "typescript": "^5.8.2"}, "publishConfig": {"ignore": ["!build/", "src/", "screenshots/"]}}