{"level":30,"time":1759026422711,"pid":4356,"hostname":"<PERSON><PERSON><PERSON>","msg":"register transformer: An<PERSON><PERSON> (endpoint: /v1/messages)"}
{"level":30,"time":1759026422711,"pid":4356,"hostname":"<PERSON><PERSON><PERSON>","msg":"register transformer: gemini (endpoint: /v1beta/models/:modelAndAction)"}
{"level":30,"time":1759026422711,"pid":4356,"hostname":"<PERSON>ele<PERSON>","msg":"register transformer: vertex-gemini (no endpoint)"}
{"level":30,"time":1759026422711,"pid":4356,"hostname":"<PERSON>ele<PERSON>","msg":"register transformer: vertex-claude (no endpoint)"}
{"level":30,"time":1759026422711,"pid":4356,"hostname":"<PERSON>ele<PERSON>","msg":"register transformer: deepseek (no endpoint)"}
{"level":30,"time":1759026422711,"pid":4356,"hostname":"<PERSON>elen","msg":"register transformer: tooluse (no endpoint)"}
{"level":30,"time":1759026422711,"pid":4356,"hostname":"Velen","msg":"register transformer: openrouter (no endpoint)"}
{"level":30,"time":1759026422711,"pid":4356,"hostname":"Velen","msg":"register transformer: OpenAI (endpoint: /v1/chat/completions)"}
{"level":30,"time":1759026422711,"pid":4356,"hostname":"Velen","msg":"register transformer: maxtoken (no endpoint)"}
{"level":30,"time":1759026422711,"pid":4356,"hostname":"Velen","msg":"register transformer: groq (no endpoint)"}
{"level":30,"time":1759026422711,"pid":4356,"hostname":"Velen","msg":"register transformer: cleancache (no endpoint)"}
{"level":30,"time":1759026422711,"pid":4356,"hostname":"Velen","msg":"register transformer: enhancetool (no endpoint)"}
{"level":30,"time":1759026422711,"pid":4356,"hostname":"Velen","msg":"register transformer: reasoning (no endpoint)"}
{"level":30,"time":1759026422711,"pid":4356,"hostname":"Velen","msg":"register transformer: sampling (no endpoint)"}
{"level":30,"time":1759026422712,"pid":4356,"hostname":"Velen","msg":"register transformer: maxcompletiontokens (no endpoint)"}
{"level":30,"time":1759026422712,"pid":4356,"hostname":"Velen","msg":"register transformer: cerebras (no endpoint)"}
{"level":30,"time":1759026422712,"pid":4356,"hostname":"Velen","msg":"register transformer: streamoptions (no endpoint)"}
{"level":30,"time":1759026422712,"pid":4356,"hostname":"Velen","msg":"register transformer: customparams (no endpoint)"}
{"level":30,"time":1759026422720,"pid":4356,"hostname":"Velen","msg":"Iflow provider registered"}
{"level":30,"time":1759026422720,"pid":4356,"hostname":"Velen","msg":"魔搭全能 provider registered"}
{"level":30,"time":1759026422720,"pid":4356,"hostname":"Velen","msg":"魔搭快速 provider registered"}
{"level":30,"time":1759026422720,"pid":4356,"hostname":"Velen","msg":"魔搭思考 provider registered"}
{"level":30,"time":1759026422720,"pid":4356,"hostname":"Velen","msg":"魔搭共享 provider registered"}
{"level":30,"time":1759026422721,"pid":4356,"hostname":"Velen","msg":"gemini-pro provider registered"}
{"level":30,"time":1759026422721,"pid":4356,"hostname":"Velen","msg":"gemini-flash provider registered"}
{"level":30,"time":1759026422721,"pid":4356,"hostname":"Velen","msg":"GeminiTier1 provider registered"}
{"level":30,"time":1759026422786,"pid":4356,"hostname":"Velen","msg":"Server listening at http://127.0.0.1:2526"}
{"level":30,"time":1759026422786,"pid":4356,"hostname":"Velen","msg":"🚀 LLMs API server listening on http://127.0.0.1:2526"}
{"level":30,"time":1759026828079,"pid":4356,"hostname":"Velen","reqId":"req-1","req":{"method":"GET","url":"/ui/","host":"127.0.0.1:2526","remoteAddress":"127.0.0.1","remotePort":12879},"msg":"incoming request"}
{"level":30,"time":1759026828090,"pid":4356,"hostname":"Velen","reqId":"req-1","res":{"statusCode":304},"responseTime":9.932999968528748,"msg":"request completed"}
{"level":30,"time":1759026828246,"pid":4356,"hostname":"Velen","reqId":"req-2","req":{"method":"GET","url":"/api/config","host":"127.0.0.1:2526","remoteAddress":"127.0.0.1","remotePort":12879},"msg":"incoming request"}
{"level":30,"time":1759026828247,"pid":4356,"hostname":"Velen","reqId":"req-3","req":{"method":"GET","url":"/favicon.ico","host":"127.0.0.1:2526","remoteAddress":"127.0.0.1","remotePort":12880},"msg":"incoming request"}
{"level":30,"time":1759026828248,"pid":4356,"hostname":"Velen","reqId":"req-3","res":{"statusCode":401},"responseTime":0.8255000114440918,"msg":"request completed"}
{"level":30,"time":1759026828251,"pid":4356,"hostname":"Velen","reqId":"req-2","res":{"statusCode":200},"responseTime":4.437099993228912,"msg":"request completed"}
{"level":30,"time":1759026828253,"pid":4356,"hostname":"Velen","reqId":"req-4","req":{"method":"GET","url":"/api/config","host":"127.0.0.1:2526","remoteAddress":"127.0.0.1","remotePort":12880},"msg":"incoming request"}
{"level":30,"time":1759026828260,"pid":4356,"hostname":"Velen","reqId":"req-4","res":{"statusCode":200},"responseTime":7.286499977111816,"msg":"request completed"}
{"level":30,"time":1759026828263,"pid":4356,"hostname":"Velen","reqId":"req-5","req":{"method":"GET","url":"/api/update/check","host":"127.0.0.1:2526","remoteAddress":"127.0.0.1","remotePort":12879},"msg":"incoming request"}
{"level":30,"time":1759026828381,"pid":4356,"hostname":"Velen","reqId":"req-6","req":{"method":"GET","url":"/api/transformers","host":"127.0.0.1:2526","remoteAddress":"127.0.0.1","remotePort":12880},"msg":"incoming request"}
{"level":30,"time":1759026828382,"pid":4356,"hostname":"Velen","reqId":"req-6","res":{"statusCode":200},"responseTime":0.5934000015258789,"msg":"request completed"}
{"level":30,"time":1759026831144,"pid":4356,"hostname":"Velen","reqId":"req-5","res":{"statusCode":200},"responseTime":2881.208399951458,"msg":"request completed"}
{"level":30,"time":1759026847807,"pid":4356,"hostname":"Velen","reqId":"req-7","req":{"method":"POST","url":"/api/config","host":"127.0.0.1:2526","remoteAddress":"127.0.0.1","remotePort":12879},"msg":"incoming request"}
{"level":30,"time":1759026847816,"pid":4356,"hostname":"Velen","reqId":"req-7","res":{"statusCode":200},"responseTime":8.779600024223328,"msg":"request completed"}
{"level":30,"time":1759026847820,"pid":4356,"hostname":"Velen","reqId":"req-8","req":{"method":"POST","url":"/api/restart","host":"127.0.0.1:2526","remoteAddress":"127.0.0.1","remotePort":12879},"msg":"incoming request"}
{"level":30,"time":1759026847821,"pid":4356,"hostname":"Velen","reqId":"req-8","res":{"statusCode":200},"responseTime":0.8797999620437622,"msg":"request completed"}
